2025-07-11T07:07:35.107Z [DEBUG] - Readability analysis library not available
2025-07-11T07:07:35.116Z [DEBUG] - Language detection library not available
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🚀 Browser pool initialized: 2 max browsers, 3 max pages per browser
2025-07-11T07:07:46.755Z [DEBUG] - Initialized 5 core accessibility patterns
2025-07-11T07:07:46.758Z [INFO] - 📚 Accessibility Pattern Library initialized - {"totalPatterns":5,"enabledCategories":6,"strictMode":false}
2025-07-11T07:07:46.761Z [INFO] - 🤖 AI-Powered Semantic Validator initialized - {"nlpAnalysis":true,"patternLibrary":true,"contextualAnalysis":true}
2025-07-11T07:07:46.763Z [INFO] - 📊 Enhanced Content Quality Analyzer initialized - {"readabilityAnalysis":true,"semanticAnalysis":true,"languageDetection":true}
2025-07-11T07:07:46.765Z [INFO] - ⚡ Modern Framework Optimizer initialized - {"svelte":true,"solidjs":true,"qwik":true,"buildTools":true}
2025-07-11T07:07:46.767Z [INFO] - 🏗️ Component Library Detector initialized - {"materialUI":true,"a ntDesign":true,"chakraUI":true,"deepAnalysis":true}
2025-07-11T07:07:46.769Z [INFO] - EnhancedColorAnalyzer initialized - {"getContrastLibrary":true,"colorJSLibrary":true,"enhancedAccuracy":true}
2025-07-11T07:07:46.772Z [INFO] - 📄 Headless CMS Detector initialized - {"strapi":true,"contentful":true,"sanity":true,"jamstack":true}
2025-07-11T07:07:46.775Z [INFO] - 🔧 Utility Integration Manager initialized
2025-07-11T07:07:46.778Z [INFO] - 🚀 Enhanced Performance Monitor initialized - {"trendAnalysis":true,"autoOptimization":true,"resourceCorrelation":true,"realTimeAlerts":true}
2025-07-11T07:07:46.780Z [INFO] - 🔗 Performance Integration Bridge initialized
2025-07-11T07:07:46.781Z [INFO] - 📊 Real-Time Monitoring Dashboard initialized
2025-07-11T07:07:46.783Z [INFO] - 🔮 Initialized 4 predictive models
2025-07-11T07:07:46.785Z [INFO] - 🔮 Predictive Performance Analytics initialized
2025-07-11T07:07:46.788Z [INFO] - 🤖 Automated Optimization Engine initialized
2025-07-11T07:07:46.790Z [INFO] - 🔌 Dashboard WebSocket Service initialized
2025-07-11T07:07:46.792Z [INFO] - 🎛️ Performance Automation Controller initialized
2025-07-11T07:07:46.942Z [INFO] - 🌐 Browser pool initialized - {"maxBrowsers":1,"maxPagesPerBrowser":3,"memoryThreshold":2048}
2025-07-11T07:07:46.948Z [INFO] - Detected VPS profile: xlarge - {"cpu":4,"memory":12,"recommendedLimits":{"maxConcurrentScans":12,"maxBrowserInstances":4,"maxPagesPerBrowser":4,"maxCacheSize":400,"maxMemoryUsage":12800,"scanTimeout":60000}}
2025-07-11T07:07:46.951Z [DEBUG] - Worker processes disabled - {"enableWorkerProcesses":false,"isMaster":true,"nodeEnv":"development"}
2025-07-11T07:07:46.953Z [DEBUG] - Connection pools configured - {"maxSockets":50,"keepAlive":true}
[INFO] Registering test-auth routes (development only)
🚀 Starting Comply Checker Backend in development mode
📋 Port: 3001
📋 Setting up API routes...
📋 Attempting to start server on port 3001...
✅ Server started successfully!
🌐 Server running on: http://localhost:3001
🏥 Health check: http://localhost:3001/health
🧪 HIPAA Analysis: http://localhost:3001/api/v1/compliance/scan
📋 Environment: development
🚀 Ready to accept HIPAA compliance requests!
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
2025-07-11T07:08:16.930Z [INFO] - 🔓 [9c274340-daae-42f5-a401-1119d0e85597] Development mode: Mock authentication applied
2025-07-11T07:08:16.990Z [INFO] - 🚀 [9c274340-daae-42f5-a401-1119d0e85597] WCAG scan request received
2025-07-11T07:08:17.005Z [INFO] - 📋 [9c274340-daae-42f5-a401-1119d0e85597] Request headers: - {"content-type":"application/json","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","authorization":"Bearer [REDACTED]"}
2025-07-11T07:08:17.026Z [INFO] - 👤 [9c274340-daae-42f5-a401-1119d0e85597] User context: - {"userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","email":"<EMAIL>","permissions":["wcag:scan","wcag:view","wcag:export"]}
2025-07-11T07:08:17.051Z [INFO] - 🎯 [9c274340-daae-42f5-a401-1119d0e85597] Target URL: https://tigerconnect.com/
2025-07-11T07:08:17.081Z [INFO] - ⚙️ [9c274340-daae-42f5-a401-1119d0e85597] Scan options: - {"enableContrastAnalysis":true,"enableKeyboardTesting":true,"enableFocusAnalysis":true,"enableSemanticValidation":true,"enableManualReview":true,"wcagVersion":"2.2","level":"AAA","maxPages":5,"timeout":60000}
2025-07-11T07:08:17.119Z [INFO] - ✅ [9c274340-daae-42f5-a401-1119d0e85597] Target URL validation passed
2025-07-11T07:08:17.150Z [INFO] - 🔧 [9c274340-daae-42f5-a401-1119d0e85597] Scan configuration created: - {"targetUrl":"https://tigerconnect.com/","userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","wcagVersion":"2.2","level":"AAA","timeout":60000}
2025-07-11T07:08:17.173Z [INFO] - 🚀 [9c274340-daae-42f5-a401-1119d0e85597] Starting comprehensive WCAG scan with orchestrator
2025-07-11T07:08:17.187Z [INFO] - 📝 Creating WCAG scan record...
2025-07-11T07:08:17.214Z [INFO] - 🆔 [9c274340-daae-42f5-a401-1119d0e85597] Scan initiated with ID: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.224Z [INFO] - 📊 [9c274340-daae-42f5-a401-1119d0e85597] Scan started successfully: - {"scanId":"4034a743-0932-44f3-967d-672066ab1bf9","status":"pending","targetUrl":"https://tigerconnect.com/"}
2025-07-11T07:08:17.249Z [INFO] - ✅ [9c274340-daae-42f5-a401-1119d0e85597] WCAG scan initiated successfully in 259ms
2025-07-11T07:08:17.284Z [INFO] - ✅ WCAG scan record created with ID: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.365Z [INFO] - ✅ WCAG scan record created with ID: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.373Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> pending
2025-07-11T07:08:17.447Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> pending
2025-07-11T07:08:17.468Z [INFO] - 🚀 Initialized scan 4034a743-0932-44f3-967d-672066ab1bf9 with 9 rules
2025-07-11T07:08:17.518Z [DEBUG] - 📊 Started performance monitoring for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.556Z [DEBUG] - 📊 Started performance monitoring for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.608Z [DEBUG] - 📊 Started performance monitoring for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.627Z [DEBUG] - 🔗 Integrated monitoring started for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.668Z [DEBUG] - 📊 Registered active scan: 4034a743-0932-44f3-967d-672066ab1bf9 (total: 1)
2025-07-11T07:08:17.718Z [INFO] - 🎛️ Starting Performance Automation System...
2025-07-11T07:08:17.755Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:17.790Z [INFO] - 📊 Real-Time Monitoring Dashboard started
2025-07-11T07:08:17.813Z [INFO] - ✅ Dashboard monitoring started
2025-07-11T07:08:17.833Z [INFO] - 🔌 Dashboard WebSocket Service started on port 8081
2025-07-11T07:08:17.852Z [INFO] - ✅ WebSocket service started
2025-07-11T07:08:17.869Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T07:08:17.907Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T07:08:17.909Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T07:08:17.910Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T07:08:17.911Z [INFO] - 🔮 Predictive Performance Analytics started
2025-07-11T07:08:17.913Z [INFO] - ✅ Predictive analytics started
2025-07-11T07:08:17.914Z [INFO] - 🎛️ Performance Automation System fully operational
2025-07-11T07:08:17.915Z [INFO] - 🎛️ Started Performance Automation Controller for scan monitoring
2025-07-11T07:08:17.917Z [DEBUG] - 🔍 Getting page for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:17.921Z [DEBUG] - 🌐 Creating new browser instance
2025-07-11T07:08:21.915Z [INFO] - ✅ Created new browser: browser-1752217701915-iqxum139x
2025-07-11T07:08:22.442Z [DEBUG] - 🆕 Created new page for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:08:22.449Z [DEBUG] - ✅ URL validation passed for: https://tigerconnect.com/
2025-07-11T07:08:22.453Z [DEBUG] - 🔍 Checking connectivity for: https://tigerconnect.com/
2025-07-11T07:08:22.762Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:25.793Z [DEBUG] - ✅ Connectivity check passed for: https://tigerconnect.com/ (status: 200)
2025-07-11T07:08:25.795Z [INFO] - 🔗 Navigating to: https://tigerconnect.com/ (timeout: 60000ms)
2025-07-11T07:08:27.775Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:32.843Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:37.858Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:42.871Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:47.957Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:52.967Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:08:58.027Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:03.032Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:05.277Z [INFO] - ✅ Navigation completed
2025-07-11T07:09:05.278Z [INFO] - 🚀 Starting Phase 2 analysis: Website type detection and optimization
2025-07-11T07:09:05.280Z [DEBUG] - 🔍 Starting CMS platform detection
2025-07-11T07:09:05.284Z [DEBUG] - 🛒 Starting e-commerce accessibility analysis
2025-07-11T07:09:05.286Z [DEBUG] - 🎬 Starting media accessibility analysis
2025-07-11T07:09:05.287Z [DEBUG] - ⚛️ Starting framework-specific accessibility analysis
2025-07-11T07:09:05.300Z [WARN] - ⚠️ Phase 2 analysis encountered errors, continuing with standard analysis - {"error":"CMS detection failed - no results returned"}
2025-07-11T07:09:05.302Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:05.342Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:05.343Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 0% (0/9)
2025-07-11T07:09:05.345Z [INFO] - 🔍 Executing 9 WCAG checks...
2025-07-11T07:09:05.347Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:05.356Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:05.356Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 0% (0/9)
2025-07-11T07:09:05.358Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Minimum) (WCAG-010)
2025-07-11T07:09:05.359Z [INFO] - 🎯 Advanced Focus Tracker initialized - {"customIndicators":true,"flowAnalysis":true,"accessibilityTree":true}
2025-07-11T07:09:05.360Z [INFO] - 🌈 Wide Gamut Color Analyzer initialized - {"p3Analysis":true,"rec2020Analysis":true,"dynamicMonitoring":true}
2025-07-11T07:09:05.364Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-010: Focus Not Obscured (Minimum)
2025-07-11T07:09:05.439Z [DEBUG] - 🔍 Cache miss: rule:WCAG-010:2b050acac20b6b1d43405d6ab7a4fae3:659731eb...
2025-07-11T07:09:05.439Z [DEBUG] - 📊 Cache stats: 0 hits, 1 misses, 0 entries
2025-07-11T07:09:05.441Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-010, executing check
2025-07-11T07:09:05.443Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-010:2b050aca:659731eb
2025-07-11T07:09:05.445Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting WCAG-010: Focus Not Obscured (Minimum)
2025-07-11T07:09:07.616Z [WARN] - ⚠️ [4034a743-0932-44f3-967d-672066ab1bf9] WCAG-010 failed: 34/100 (34.0%) - converted to 0% for WCAG compliance
2025-07-11T07:09:07.616Z [INFO] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Completed WCAG-010 in 2171ms - Status: failed (0/100)
2025-07-11T07:09:07.619Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-010
2025-07-11T07:09:07.621Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:07.621Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:07.623Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:07.625Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:07.686Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:08.043Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:08.705Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:08.721Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:08.722Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1101}
2025-07-11T07:09:08.728Z [DEBUG] - 💾 Cached: rule:WCAG-010:2b050acac20b6b1d43405d6ab7a4fae3:659731ebf75384e3bc2f1e9e285145a8 (77955 bytes)
2025-07-11T07:09:08.728Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-010
2025-07-11T07:09:08.731Z [DEBUG] - 🔍 Cache miss: rule:WCAG-010:WCAG-010:dGV4dDpGb2N1cyBvYnN0cnVjdGlvbiBh...
2025-07-11T07:09:08.732Z [DEBUG] - 📊 Cache stats: 0 hits, 2 misses, 1 entries
2025-07-11T07:09:08.736Z [DEBUG] - 📊 Reached max evidence items limit: 30
2025-07-11T07:09:08.737Z [DEBUG] - 💾 Cached: rule:WCAG-010:WCAG-010:dGV4dDpGb2N1cyBvYnN0cnVjdGlvbiBh (34703 bytes)
2025-07-11T07:09:08.737Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:08.738Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:08.740Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:08.741Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:08.784Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:09.799Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:09.814Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:09.815Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1078}
2025-07-11T07:09:09.817Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-010: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:09.817Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-010","Utility errors detected for WCAG-010: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:09.818Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-010
2025-07-11T07:09:09.820Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-010: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:09.823Z [DEBUG] - 🔧 Utility performance recorded for WCAG-010: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:09.825Z [DEBUG] - 🔧 Utility analysis completed for WCAG-010: - {"utilitiesUsed":2,"errors":1,"executionTime":4458}
2025-07-11T07:09:09.826Z [DEBUG] - ⏱️ Check WCAG-010 completed in 4468ms (success: true)
2025-07-11T07:09:09.826Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:09.833Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:09.833Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 11% (1/9)
2025-07-11T07:09:09.834Z [INFO] - ✅ Rule WCAG-010 completed: failed (0/100)
2025-07-11T07:09:09.835Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:09.841Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:09.842Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 11% (1/9)
2025-07-11T07:09:09.843Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Enhanced) (WCAG-011)
2025-07-11T07:09:09.845Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-011: Focus Not Obscured (Enhanced)
2025-07-11T07:09:09.910Z [DEBUG] - 🔍 Cache miss: rule:WCAG-011:2b050acac20b6b1d43405d6ab7a4fae3:1b3f6049...
2025-07-11T07:09:09.911Z [DEBUG] - 📊 Cache stats: 0 hits, 3 misses, 2 entries
2025-07-11T07:09:09.913Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-011, executing check
2025-07-11T07:09:09.915Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-011:2b050aca:1b3f6049
2025-07-11T07:09:09.916Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting WCAG-011: Focus Not Obscured (Enhanced)
2025-07-11T07:09:11.979Z [WARN] - ⚠️ [4034a743-0932-44f3-967d-672066ab1bf9] WCAG-011 failed: 94/100 (94.0%) - converted to 0% for WCAG compliance
2025-07-11T07:09:11.980Z [INFO] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Completed WCAG-011 in 2063ms - Status: failed (0/100)
2025-07-11T07:09:11.982Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-011
2025-07-11T07:09:11.984Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:11.985Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:11.986Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:11.987Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:12.036Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:13.037Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:13.045Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:13.053Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:13.053Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1069}
2025-07-11T07:09:13.057Z [DEBUG] - 💾 Cached: rule:WCAG-011:2b050acac20b6b1d43405d6ab7a4fae3:1b3f6049c2fd04a00f8191576e3a24c5 (72659 bytes)
2025-07-11T07:09:13.059Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-011
2025-07-11T07:09:13.060Z [DEBUG] - 🔍 Cache miss: rule:WCAG-011:WCAG-011:dGV4dDpFbmhhbmNlZCBmb2N1cyB2aXNp...
2025-07-11T07:09:13.062Z [DEBUG] - 📊 Cache stats: 0 hits, 4 misses, 3 entries
2025-07-11T07:09:13.063Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced focus visibility analysis summary
2025-07-11T07:09:13.065Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T07:09:13.066Z [DEBUG] - 💾 Cached: rule:WCAG-011:WCAG-011:dGV4dDpFbmhhbmNlZCBmb2N1cyB2aXNp (31782 bytes)
2025-07-11T07:09:13.066Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:13.067Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:13.069Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:13.070Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:13.115Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:14.121Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:14.138Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:14.138Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1072}
2025-07-11T07:09:14.139Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-011: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:14.140Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-011","Utility errors detected for WCAG-011: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:14.141Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-011
2025-07-11T07:09:14.142Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-011: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:14.146Z [DEBUG] - 🔧 Utility performance recorded for WCAG-011: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:14.147Z [DEBUG] - 🔧 Utility analysis completed for WCAG-011: - {"utilitiesUsed":2,"errors":1,"executionTime":4296}
2025-07-11T07:09:14.148Z [DEBUG] - ⏱️ Check WCAG-011 completed in 4305ms (success: true)
2025-07-11T07:09:14.149Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:14.155Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:14.156Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 22% (2/9)
2025-07-11T07:09:14.156Z [INFO] - ✅ Rule WCAG-011 completed: failed (0/100)
2025-07-11T07:09:14.157Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:14.163Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:14.165Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 22% (2/9)
2025-07-11T07:09:14.166Z [INFO] - 🔍 Executing rule: Focus Appearance (WCAG-012)
2025-07-11T07:09:14.168Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-012: Focus Appearance
2025-07-11T07:09:14.233Z [DEBUG] - 🔍 Cache miss: rule:WCAG-012:2b050acac20b6b1d43405d6ab7a4fae3:049c959d...
2025-07-11T07:09:14.234Z [DEBUG] - 📊 Cache stats: 0 hits, 5 misses, 4 entries
2025-07-11T07:09:14.236Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-012, executing check
2025-07-11T07:09:14.238Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-012:2b050aca:049c959d
2025-07-11T07:09:14.239Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting WCAG-012: Focus Appearance
2025-07-11T07:09:14.266Z [DEBUG] - 🌈 Starting wide gamut color analysis
2025-07-11T07:09:14.266Z [DEBUG] - 🎨 Starting enhanced color contrast analysis
2025-07-11T07:09:14.310Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.315Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.330Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.337Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.344Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.350Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.360Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.366Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.376Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.383Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.391Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.396Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.402Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.407Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.412Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.418Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.423Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.429Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.434Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.439Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.445Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.451Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.457Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.462Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.467Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.472Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.476Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.483Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.488Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.492Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.498Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.503Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.508Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.514Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.519Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.523Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.530Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.534Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.539Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.544Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.550Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.554Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.561Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.567Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.572Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.577Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.583Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.587Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.593Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.599Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.605Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.612Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.617Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.622Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.626Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.631Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.636Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.641Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.645Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.649Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.654Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.658Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.664Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.669Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.675Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.681Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.686Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.691Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.696Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.701Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.705Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.709Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.714Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.720Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.724Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.729Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.734Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.738Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.741Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.747Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.751Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.756Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.772Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.784Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.793Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.799Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.806Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:14.949Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.040Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.106Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.114Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.120Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.125Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.130Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.137Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.142Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.149Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.155Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.163Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.168Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.172Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.179Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.184Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.191Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.196Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.200Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.204Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.209Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.214Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.219Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.224Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.230Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.235Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.241Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.248Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.253Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.258Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.263Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.269Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.274Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.279Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.283Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.288Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.292Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.296Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.301Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.305Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.309Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.314Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.318Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.323Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.327Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.332Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.337Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.340Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.345Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.351Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.356Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.362Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.367Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.373Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.379Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.385Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.390Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.396Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.401Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.406Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.412Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.417Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.421Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.429Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.434Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.438Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.443Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.448Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.453Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.458Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.464Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.468Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.473Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.479Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.484Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.489Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.495Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.500Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.505Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.511Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.515Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.520Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.525Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.530Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.535Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.541Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.546Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.549Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.555Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.558Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.562Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.567Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.571Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.575Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.579Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.584Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.589Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.593Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.598Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.602Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.606Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.610Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.614Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.619Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.623Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.627Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.633Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.638Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.642Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.647Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.651Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.655Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.659Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.665Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.669Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.676Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.681Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.685Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.689Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.695Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.699Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.702Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.707Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.712Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.716Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.720Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.724Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.730Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.734Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.739Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.744Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.748Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.753Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.758Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.763Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.767Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.772Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.778Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.782Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.788Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.794Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.798Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.805Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.811Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.817Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.822Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.827Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.832Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.837Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.841Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.846Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.852Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.856Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.861Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.866Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.871Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.875Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.881Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.886Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.891Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.897Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.903Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.908Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.915Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.919Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.925Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.931Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.936Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.941Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.946Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.951Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.956Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.962Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.966Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.971Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.978Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.982Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.988Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.993Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:15.997Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:16.003Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:16.007Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:16.012Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:16.016Z [DEBUG] - Using get-contrast library for enhanced accuracy
2025-07-11T07:09:16.016Z [INFO] - ✅ Enhanced color analysis completed: 271 elements analyzed
Advanced focus appearance analysis failed, falling back to basic analysis: Error [TypeError]: element.className.split is not a function
Object.getElementSelector (pptr:evaluate;WideGamutColorAnalyzer.injectWideGamutAnalysisFunctions%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cutils%5Cwide-gamut-color-analyzer.ts%3A168%3A20):45:54)
    at evaluate (evaluate at WideGamutColorAnalyzer.analyzeWideGamutColors (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\wide-gamut-color-analyzer.ts:60:43), <anonymous>:2:52)
    at ExecutionContext.#evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\cdp\ExecutionContext.ts:452:34)
    at async ExecutionContext.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\cdp\ExecutionContext.ts:291:12)
    at async IsolatedWorld.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\cdp\IsolatedWorld.ts:196:12)
    at async CdpFrame.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\api\Frame.ts:490:12)
    at async CdpPage.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\api\Page.ts:2308:12)
    at async WideGamutColorAnalyzer.analyzeWideGamutColors (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\wide-gamut-color-analyzer.ts:186:28)
    at async FocusAppearanceCheck.executeFocusAppearanceCheck (D:\Web projects\Comply Checker\backend\src\compliance\wcag\checks\focus-appearance.ts:113:34)
    at async EnhancedCheckTemplate.executeCheck (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\check-template.ts:73:24)
    at async EnhancedCheckTemplate.executeEnhancedCheck (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\enhanced-check-template.ts:221:22)
2025-07-11T07:09:17.163Z [WARN] - ⚠️ [4034a743-0932-44f3-967d-672066ab1bf9] WCAG-012 failed: 22/100 (22.0%) - converted to 0% for WCAG compliance
2025-07-11T07:09:17.164Z [INFO] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Completed WCAG-012 in 2924ms - Status: failed (0/100)
2025-07-11T07:09:17.166Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-012
2025-07-11T07:09:17.168Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:17.169Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:17.171Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:17.172Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:17.241Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:17.878Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T07:09:17.878Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T07:09:17.880Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T07:09:17.882Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T07:09:18.061Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:18.254Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:18.269Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:18.270Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1102}
2025-07-11T07:09:18.275Z [DEBUG] - 💾 Cached: rule:WCAG-012:2b050acac20b6b1d43405d6ab7a4fae3:049c959d2823ba0d57461e7b8cb5c4be (59440 bytes)
2025-07-11T07:09:18.275Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-012
2025-07-11T07:09:18.280Z [DEBUG] - 🔍 Cache miss: rule:WCAG-012:WCAG-012:dGV4dDpGb2N1cyBhcHBlYXJhbmNlIGFu...
2025-07-11T07:09:18.281Z [DEBUG] - 📊 Cache stats: 0 hits, 6 misses, 5 entries
2025-07-11T07:09:18.284Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T07:09:18.285Z [DEBUG] - 💾 Cached: rule:WCAG-012:WCAG-012:dGV4dDpGb2N1cyBhcHBlYXJhbmNlIGFu (19513 bytes)
2025-07-11T07:09:18.285Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:18.286Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:18.288Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:18.289Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:18.334Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:19.340Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:19.355Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:19.355Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1070}
2025-07-11T07:09:19.357Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-012: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:19.358Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-012","Utility errors detected for WCAG-012: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:19.359Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-012
2025-07-11T07:09:19.360Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-012: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:19.364Z [DEBUG] - 🔧 Utility performance recorded for WCAG-012: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:19.365Z [DEBUG] - 🔧 Utility analysis completed for WCAG-012: - {"utilitiesUsed":2,"errors":1,"executionTime":5191}
2025-07-11T07:09:19.366Z [DEBUG] - ⏱️ Check WCAG-012 completed in 5200ms (success: true)
2025-07-11T07:09:19.367Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:19.375Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:19.375Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 33% (3/9)
2025-07-11T07:09:19.376Z [INFO] - ✅ Rule WCAG-012 completed: failed (0/100)
2025-07-11T07:09:19.377Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:19.382Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:19.383Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 33% (3/9)
2025-07-11T07:09:19.384Z [INFO] - 🔍 Executing rule: Dragging Movements (WCAG-013)
2025-07-11T07:09:19.386Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-013: Dragging Movements
2025-07-11T07:09:19.464Z [DEBUG] - 🔍 Cache miss: rule:WCAG-013:2b050acac20b6b1d43405d6ab7a4fae3:5666d327...
2025-07-11T07:09:19.464Z [DEBUG] - 📊 Cache stats: 0 hits, 7 misses, 6 entries
2025-07-11T07:09:19.466Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-013, executing check
2025-07-11T07:09:19.468Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-013:2b050aca:5666d327
2025-07-11T07:09:19.469Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Executing manual review check WCAG-013
2025-07-11T07:09:19.524Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-013
2025-07-11T07:09:19.525Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:19.527Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:19.530Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:19.566Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:20.576Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:20.600Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:20.600Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1075}
2025-07-11T07:09:20.602Z [DEBUG] - 💾 Cached: rule:WCAG-013:2b050acac20b6b1d43405d6ab7a4fae3:5666d327c6c50696d33438b7c9208552 (3004 bytes)
2025-07-11T07:09:20.603Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-013
2025-07-11T07:09:20.604Z [DEBUG] - 🔍 Cache miss: rule:WCAG-013:WCAG-013:dGV4dDpObyBkcmFnIGFuZCBkcm9wIGVs...
2025-07-11T07:09:20.605Z [DEBUG] - 📊 Cache stats: 0 hits, 8 misses, 7 entries
2025-07-11T07:09:20.606Z [DEBUG] - 💾 Cached: rule:WCAG-013:WCAG-013:dGV4dDpObyBkcmFnIGFuZCBkcm9wIGVs (4316 bytes)
2025-07-11T07:09:20.606Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:20.608Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:20.609Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:20.644Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:21.646Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:21.665Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:21.665Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1059}
2025-07-11T07:09:21.666Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-013: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:21.667Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-013","Utility errors detected for WCAG-013: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:21.668Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-013
2025-07-11T07:09:21.669Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-013: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:21.670Z [DEBUG] - 🔧 Utility performance recorded for WCAG-013: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:21.671Z [DEBUG] - 🔧 Utility analysis completed for WCAG-013: - {"utilitiesUsed":1,"errors":1,"executionTime":2282}
2025-07-11T07:09:21.672Z [DEBUG] - ⏱️ Check WCAG-013 completed in 2288ms (success: true)
2025-07-11T07:09:21.673Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:21.680Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:21.681Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 44% (4/9)
2025-07-11T07:09:21.682Z [INFO] - ✅ Rule WCAG-013 completed: failed (0/100)
2025-07-11T07:09:21.683Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:21.689Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:21.689Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 44% (4/9)
2025-07-11T07:09:21.690Z [INFO] - 🔍 Executing rule: Target Size (Minimum) (WCAG-014)
2025-07-11T07:09:21.691Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-014: Target Size
2025-07-11T07:09:21.777Z [DEBUG] - 🔍 Cache miss: rule:WCAG-014:2b050acac20b6b1d43405d6ab7a4fae3:844ad961...
2025-07-11T07:09:21.777Z [DEBUG] - 📊 Cache stats: 0 hits, 9 misses, 8 entries
2025-07-11T07:09:21.779Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-014, executing check
2025-07-11T07:09:21.781Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-014:2b050aca:844ad961
2025-07-11T07:09:21.782Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting WCAG-014: Target Size
2025-07-11T07:09:21.784Z [DEBUG] - 🎯 Starting enhanced target size analysis
2025-07-11T07:09:22.081Z [WARN] - ⚠️ [4034a743-0932-44f3-967d-672066ab1bf9] WCAG-014 failed: 94/100 (94.0%) - converted to 0% for WCAG compliance
2025-07-11T07:09:22.081Z [INFO] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Completed WCAG-014 in 299ms - Status: failed (0/100)
2025-07-11T07:09:22.083Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-014
2025-07-11T07:09:22.085Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:22.086Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:22.088Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:22.135Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:23.075Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:23.140Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:23.155Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:23.155Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1070}
2025-07-11T07:09:23.160Z [DEBUG] - 💾 Cached: rule:WCAG-014:2b050acac20b6b1d43405d6ab7a4fae3:844ad96101674e4953acdc3441917f7d (103687 bytes)
2025-07-11T07:09:23.160Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-014
2025-07-11T07:09:23.163Z [DEBUG] - 🔍 Cache miss: rule:WCAG-014:WCAG-014:dGV4dDpUYXJnZXQgc2l6ZSBhbmFseXNp...
2025-07-11T07:09:23.165Z [DEBUG] - 📊 Cache stats: 0 hits, 10 misses, 9 entries
2025-07-11T07:09:23.166Z [DEBUG] - 🔍 Skipping low-quality evidence: Target size analysis summary
2025-07-11T07:09:23.169Z [DEBUG] - 📊 Reached max evidence items limit: 40
2025-07-11T07:09:23.171Z [DEBUG] - 💾 Cached: rule:WCAG-014:WCAG-014:dGV4dDpUYXJnZXQgc2l6ZSBhbmFseXNp (68607 bytes)
2025-07-11T07:09:23.171Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T07:09:23.172Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:23.174Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:23.211Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:24.226Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:24.241Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:24.242Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1071}
2025-07-11T07:09:24.243Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-014: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:24.244Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-014","Utility errors detected for WCAG-014: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:24.245Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-014
2025-07-11T07:09:24.246Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-014: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:24.247Z [DEBUG] - 🔧 Utility performance recorded for WCAG-014: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:24.250Z [DEBUG] - 🔧 Utility analysis completed for WCAG-014: - {"utilitiesUsed":1,"errors":1,"executionTime":2553}
2025-07-11T07:09:24.252Z [DEBUG] - ⏱️ Check WCAG-014 completed in 2562ms (success: true)
2025-07-11T07:09:24.253Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:24.260Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:24.261Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 56% (5/9)
2025-07-11T07:09:24.262Z [INFO] - ✅ Rule WCAG-014 completed: failed (0/100)
2025-07-11T07:09:24.263Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:24.269Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:24.271Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 56% (5/9)
2025-07-11T07:09:24.272Z [INFO] - 🔍 Executing rule: Consistent Help (WCAG-015)
2025-07-11T07:09:24.274Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-015: Consistent Help
2025-07-11T07:09:24.343Z [DEBUG] - 🔍 Cache miss: rule:WCAG-015:2b050acac20b6b1d43405d6ab7a4fae3:c6e979e6...
2025-07-11T07:09:24.343Z [DEBUG] - 📊 Cache stats: 0 hits, 11 misses, 10 entries
2025-07-11T07:09:24.345Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-015, executing check
2025-07-11T07:09:24.347Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-015:2b050aca:c6e979e6
2025-07-11T07:09:24.348Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting WCAG-015: Consistent Help
2025-07-11T07:09:24.399Z [INFO] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Completed WCAG-015 in 51ms - Status: passed (100/100)
2025-07-11T07:09:24.399Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-015
2025-07-11T07:09:24.401Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:24.404Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:24.405Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T07:09:24.450Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T07:09:24.461Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:25.476Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:25.492Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:25.493Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality"],"confidence":0.6,"accuracy":0.30000000000000004,"executionTime":1092}
2025-07-11T07:09:25.494Z [DEBUG] - 💾 Cached: rule:WCAG-015:2b050acac20b6b1d43405d6ab7a4fae3:c6e979e636d3a2648d516983248f4dc1 (2208 bytes)
2025-07-11T07:09:25.494Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-015
2025-07-11T07:09:25.496Z [DEBUG] - 🔍 Cache miss: rule:WCAG-035:WCAG-035:aW5mbzpGb3VuZCA1IGhlbHAgbWVjaGFu...
2025-07-11T07:09:25.498Z [DEBUG] - 📊 Cache stats: 0 hits, 12 misses, 11 entries
2025-07-11T07:09:25.500Z [DEBUG] - 💾 Cached: rule:WCAG-035:WCAG-035:aW5mbzpGb3VuZCA1IGhlbHAgbWVjaGFu (3118 bytes)
2025-07-11T07:09:25.501Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-11T07:09:25.502Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:25.504Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T07:09:25.539Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T07:09:25.548Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:26.570Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:26.585Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:26.585Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality"],"confidence":0.6,"accuracy":0.30000000000000004,"executionTime":1084}
2025-07-11T07:09:26.587Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-015: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:26.588Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-015","Utility errors detected for WCAG-015: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:26.589Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-015
2025-07-11T07:09:26.590Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-015: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:26.591Z [DEBUG] - 🔧 Utility performance recorded for WCAG-015: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:26.594Z [DEBUG] - 🔧 Utility analysis completed for WCAG-015: - {"utilitiesUsed":1,"errors":1,"executionTime":2314}
2025-07-11T07:09:26.596Z [DEBUG] - ⏱️ Check WCAG-015 completed in 2324ms (success: true)
2025-07-11T07:09:26.597Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:26.603Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:26.603Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 67% (6/9)
2025-07-11T07:09:26.604Z [INFO] - ✅ Rule WCAG-015 completed: passed (100/100)
2025-07-11T07:09:26.605Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:26.611Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:26.613Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 67% (6/9)
2025-07-11T07:09:26.614Z [INFO] - 🔍 Executing rule: Redundant Entry (WCAG-016)
2025-07-11T07:09:26.616Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-016: Redundant Entry
2025-07-11T07:09:26.682Z [DEBUG] - 🔍 Cache miss: rule:WCAG-016:2b050acac20b6b1d43405d6ab7a4fae3:b4fa02fc...
2025-07-11T07:09:26.683Z [DEBUG] - 📊 Cache stats: 0 hits, 13 misses, 12 entries
2025-07-11T07:09:26.685Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-016, executing check
2025-07-11T07:09:26.687Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-016:2b050aca:b4fa02fc
2025-07-11T07:09:26.688Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Executing manual review check WCAG-016
2025-07-11T07:09:26.704Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-016
2025-07-11T07:09:26.704Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:26.705Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:26.707Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:26.741Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:27.757Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:27.773Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:27.773Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1069}
2025-07-11T07:09:27.775Z [DEBUG] - 💾 Cached: rule:WCAG-016:2b050acac20b6b1d43405d6ab7a4fae3:b4fa02fc20908e9ce39ed45e000f147e (1275 bytes)
2025-07-11T07:09:27.775Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-016
2025-07-11T07:09:27.777Z [DEBUG] - 🔍 Cache miss: rule:WCAG-016:WCAG-016:dGV4dDpSZWR1bmRhbnQgZW50cnkgYW5h...
2025-07-11T07:09:27.778Z [DEBUG] - 📊 Cache stats: 0 hits, 14 misses, 13 entries
2025-07-11T07:09:27.779Z [DEBUG] - 💾 Cached: rule:WCAG-016:WCAG-016:dGV4dDpSZWR1bmRhbnQgZW50cnkgYW5h (1576 bytes)
2025-07-11T07:09:27.781Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:27.783Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:27.785Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:27.819Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:28.081Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T07:09:28.836Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:28.852Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:28.853Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1071}
2025-07-11T07:09:28.854Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-016: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:28.854Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-016","Utility errors detected for WCAG-016: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:28.856Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-016
2025-07-11T07:09:28.856Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-016: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:28.859Z [DEBUG] - 🔧 Utility performance recorded for WCAG-016: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:28.861Z [DEBUG] - 🔧 Utility analysis completed for WCAG-016: - {"utilitiesUsed":1,"errors":1,"executionTime":2239}
2025-07-11T07:09:28.862Z [DEBUG] - ⏱️ Check WCAG-016 completed in 2248ms (success: true)
2025-07-11T07:09:28.863Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:28.879Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:28.880Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 78% (7/9)
2025-07-11T07:09:28.881Z [INFO] - ✅ Rule WCAG-016 completed: failed (0/100)
2025-07-11T07:09:28.883Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:28.893Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:28.894Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 78% (7/9)
2025-07-11T07:09:28.895Z [INFO] - 🔍 Executing rule: Accessible Authentication (Minimum) (WCAG-022)
2025-07-11T07:09:28.896Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting enhanced WCAG-022: Accessible Authentication (Minimum)
2025-07-11T07:09:28.961Z [DEBUG] - 🔍 Cache miss: rule:WCAG-022:2b050acac20b6b1d43405d6ab7a4fae3:a916f1b5...
2025-07-11T07:09:28.962Z [DEBUG] - 📊 Cache stats: 0 hits, 15 misses, 14 entries
2025-07-11T07:09:28.963Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache miss for WCAG-022, executing check
2025-07-11T07:09:28.966Z [DEBUG] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Cache key: rule:WCAG-022:2b050aca:a916f1b5
2025-07-11T07:09:28.967Z [INFO] - 🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Executing manual review check WCAG-022
2025-07-11T07:09:28.979Z [ERROR] - Error in accessible authentication check: - {"error":"this.getElementSelector is not a function\npptr:evaluate;AccessibleAuthenticationCheck.detectCognitiveFunctionTests%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Caccessible-authentication.ts%3A352%3A27):19:40\nNodeList.forEach (<anonymous>)"}
2025-07-11T07:09:28.980Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Applying utility enhancements for WCAG-022
2025-07-11T07:09:28.981Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T07:09:28.984Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:28.985Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:28.986Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:29.026Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:30.041Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:30.056Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:30.056Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1075}
2025-07-11T07:09:30.058Z [DEBUG] - 💾 Cached: rule:WCAG-022:2b050acac20b6b1d43405d6ab7a4fae3:a916f1b5c41fd4a4001d720d0fd2b0ea (1427 bytes)
2025-07-11T07:09:30.058Z [DEBUG] - 💾 [4034a743-0932-44f3-967d-672066ab1bf9] Cached result for WCAG-022
2025-07-11T07:09:30.060Z [DEBUG] - 🔍 Cache miss: rule:WCAG-022:WCAG-022:ZXJyb3I6RXJyb3IgZHVyaW5nIGF1dGhl...
2025-07-11T07:09:30.060Z [DEBUG] - 📊 Cache stats: 0 hits, 16 misses, 15 entries
2025-07-11T07:09:30.062Z [DEBUG] - 💾 Cached: rule:WCAG-022:WCAG-022:ZXJyb3I6RXJyb3IgZHVyaW5nIGF1dGhl (1368 bytes)
2025-07-11T07:09:30.064Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:30.067Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:30.069Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:30.070Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:30.119Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:31.130Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:31.146Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:31.147Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1083}
2025-07-11T07:09:31.148Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-022: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:31.148Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-022","Utility errors detected for WCAG-022: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:31.149Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-022
2025-07-11T07:09:31.150Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-022: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:31.151Z [DEBUG] - 🔧 Utility performance recorded for WCAG-022: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:31.154Z [DEBUG] - 🔧 Utility analysis completed for WCAG-022: - {"utilitiesUsed":2,"errors":1,"executionTime":2252}
2025-07-11T07:09:31.157Z [DEBUG] - ⏱️ Check WCAG-022 completed in 2262ms (success: true)
2025-07-11T07:09:31.158Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:31.163Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:31.163Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 89% (8/9)
2025-07-11T07:09:31.164Z [INFO] - ✅ Rule WCAG-022 completed: failed (0/100)
2025-07-11T07:09:31.165Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:31.170Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:31.170Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 89% (8/9)
2025-07-11T07:09:31.171Z [INFO] - 🔍 Executing rule: Accessible Authentication (Enhanced) (WCAG-023)
🔍 [4034a743-0932-44f3-967d-672066ab1bf9] Starting WCAG-037: Accessible Authentication (Enhanced) (40% automated)
✅ [4034a743-0932-44f3-967d-672066ab1bf9] Completed WCAG-037 in 17ms - Status: passed (100/100, Manual items: 1)
2025-07-11T07:09:31.190Z [DEBUG] - 🔍 Cache miss: rule:WCAG-037:WCAG-037:aW5mbzpDb2duaXRpdmUgbG9hZCBhc3Nl...
2025-07-11T07:09:31.191Z [DEBUG] - 📊 Cache stats: 0 hits, 17 misses, 16 entries
2025-07-11T07:09:31.193Z [DEBUG] - 💾 Cached: rule:WCAG-037:WCAG-037:aW5mbzpDb2duaXRpdmUgbG9hZCBhc3Nl (3860 bytes)
2025-07-11T07:09:31.195Z [DEBUG] - 🔧 [4034a743-0932-44f3-967d-672066ab1bf9] Starting utility analysis for WCAG-023 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T07:09:31.197Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:31.199Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T07:09:31.200Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T07:09:31.240Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T07:09:32.242Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T07:09:32.269Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T07:09:32.269Z [DEBUG] - ✅ [4034a743-0932-44f3-967d-672066ab1bf9] Utility analysis completed for WCAG-023 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1074}
2025-07-11T07:09:32.270Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-023: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T07:09:32.271Z [WARN] - ⚠️ Performance alerts for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"alerts":["Low cache hit rate (0.0%) for WCAG-023","Utility errors detected for WCAG-023: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T07:09:32.272Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Low cache hit rate (0.0%) for WCAG-023
2025-07-11T07:09:32.273Z [INFO] - Alert for scan 4034a743-0932-44f3-967d-672066ab1bf9: Utility errors detected for WCAG-023: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T07:09:32.274Z [DEBUG] - 🔧 Utility performance recorded for WCAG-023: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T07:09:32.275Z [DEBUG] - 🔧 Utility analysis completed for WCAG-023: - {"utilitiesUsed":2,"errors":1,"executionTime":1099}
2025-07-11T07:09:32.276Z [DEBUG] - ⏱️ Check WCAG-023 completed in 1105ms (success: true)
2025-07-11T07:09:32.277Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:32.286Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> running
2025-07-11T07:09:32.286Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 100% (9/9)
2025-07-11T07:09:32.287Z [INFO] - ✅ Rule WCAG-023 completed: passed (100/100)
2025-07-11T07:09:32.288Z [INFO] - ✅ All checks completed: 9 results
🔍 WCAG Scoring Debug - Input Results: [
  {
    ruleId: 'WCAG-010',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-011',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-012',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-013',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-014',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-015',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-016',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-022',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.05
  },
  {
    ruleId: 'WCAG-023',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.03
  }
]
📊 Rule WCAG-010: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-011: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-012: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-013: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-014: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-015: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-016: score=0/100 (0.0%), weight=0.003
📊 Rule WCAG-022: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-023: score=100/100 (100.0%), weight=0.003
🎯 WCAG Final Score Calculation:
      - Total Weighted Score: 0.53
      - Total Weight: 0.036
      - Final Score: 15%
      - Rules Processed: 9/9
      - Passed Rules: 2
      - Failed Rules: 7
2025-07-11T07:09:32.312Z [INFO] - 💾 Saving WCAG scan result: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.313Z [INFO] - 📊 Scan summary: {"scanId":"4034a743-0932-44f3-967d-672066ab1bf9","totalRules":9,"overallScore":15,"levelAchieved":"None","riskLevel":"Critical"}
2025-07-11T07:09:32.314Z [INFO] - 💾 Saving WCAG scan result: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.360Z [INFO] - ✅ WCAG scan result saved: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.361Z [INFO] - ✅ WCAG scan result saved successfully: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.364Z [INFO] - 📝 Storing 1 manual review items for scan 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.370Z [INFO] - ✅ Stored 1 manual review items for scan 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.370Z [INFO] - ✅ Stored 1 manual review items for scan 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.371Z [INFO] - 📝 Updating WCAG scan status: 4034a743-0932-44f3-967d-672066ab1bf9 -> completed
2025-07-11T07:09:32.377Z [INFO] - ✅ WCAG scan status updated: 4034a743-0932-44f3-967d-672066ab1bf9 -> completed
2025-07-11T07:09:32.378Z [INFO] - 📈 Scan 4034a743-0932-44f3-967d-672066ab1bf9: 100% (9/9)
2025-07-11T07:09:32.379Z [INFO] - ✅ Comprehensive WCAG scan completed: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.380Z [INFO] - 📊 Overall Score: 15% | Level: None | Risk: Critical
2025-07-11T07:09:32.382Z [INFO] - 📈 New performance baseline established - {"performanceScore":80,"averageCheckDuration":2973.5555555555557,"memoryPeakMB":681}
2025-07-11T07:09:32.382Z [INFO] - 📊 Performance report generated for scan: 4034a743-0932-44f3-967d-672066ab1bf9 - {"duration":74773,"checksExecuted":9,"successRate":100,"memoryPeak":681,"performanceScore":80}
2025-07-11T07:09:32.383Z [INFO] - 📊 Performance report for scan 4034a743-0932-44f3-967d-672066ab1bf9: - {"duration":74773,"performanceScore":80,"memoryPeak":681,"recommendations":["Consider enabling more aggressive caching to reduce scan time","Optimize slow checks: WCAG-012","Low browser pool efficiency - consider increasing pool size"]}
2025-07-11T07:09:32.502Z [DEBUG] - ✅ Released page back to pool for scan: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:32.503Z [DEBUG] - 📊 Unregistered active scan: 4034a743-0932-44f3-967d-672066ab1bf9 (total: 0)
2025-07-11T07:09:32.505Z [INFO] - ✅ [9c274340-daae-42f5-a401-1119d0e85597] WCAG scan completed successfully: 4034a743-0932-44f3-967d-672066ab1bf9
2025-07-11T07:09:33.093Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
2025-07-11T05:24:00.962Z [INFO] - 🔓 [448db0f7-390f-4996-a215-72477d98a026] Development mode: Mock authentication applied
2025-07-11T05:24:00.966Z [INFO] - 📊 [448db0f7-390f-4996-a215-72477d98a026] Fetching WCAG dashboard data for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b
2025-07-11T05:24:00.969Z [INFO] - 📊 Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b - {"options":{"page":1,"limit":10,"sortBy":"scanTimestamp","sortOrder":"desc"}}
2025-07-11T05:24:01.022Z [INFO] - ✅ [448db0f7-390f-4996-a215-72477d98a026] WCAG dashboard data fetched successfully
2025-07-11T05:24:48.227Z [INFO] - 🔓 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Development mode: Mock authentication applied
2025-07-11T05:24:48.323Z [INFO] - 🚀 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] WCAG scan request received
2025-07-11T05:24:48.389Z [INFO] - 📋 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Request headers: - {"content-type":"application/json","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36","authorization":"Bearer [REDACTED]"}
2025-07-11T05:24:48.409Z [INFO] - 👤 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] User context: - {"userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","email":"<EMAIL>","permissions":["wcag:scan","wcag:view","wcag:export"]}
2025-07-11T05:24:48.439Z [INFO] - 🎯 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Target URL: https://tigerconnect.com/
2025-07-11T05:24:48.449Z [INFO] - ⚙️ [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Scan options: - {"enableContrastAnalysis":true,"enableKeyboardTesting":true,"enableFocusAnalysis":true,"enableSemanticValidation":true,"enableManualReview":true,"wcagVersion":"2.2","level":"AAA","maxPages":5,"timeout":60000}
2025-07-11T05:24:48.490Z [INFO] - ✅ [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Target URL validation passed
2025-07-11T05:24:48.510Z [INFO] - 🔧 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Scan configuration created: - {"targetUrl":"https://tigerconnect.com/","userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","wcagVersion":"2.2","level":"AAA","timeout":60000}
2025-07-11T05:24:48.511Z [INFO] - 🚀 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Starting comprehensive WCAG scan with orchestrator
2025-07-11T05:24:48.582Z [INFO] - 📝 Creating WCAG scan record...
2025-07-11T05:24:48.619Z [INFO] - 🆔 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Scan initiated with ID: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:48.624Z [INFO] - 📊 [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] Scan started successfully: - {"scanId":"65d64a12-a4dc-4241-b9df-7fe99a58764d","status":"pending","targetUrl":"https://tigerconnect.com/"}
2025-07-11T05:24:48.629Z [INFO] - ✅ [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] WCAG scan initiated successfully in 306ms
2025-07-11T05:24:49.159Z [INFO] - ✅ WCAG scan record created with ID: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.160Z [INFO] - ✅ WCAG scan record created with ID: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.220Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> pending
2025-07-11T05:24:49.369Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> pending
2025-07-11T05:24:49.375Z [INFO] - 🚀 Initialized scan 65d64a12-a4dc-4241-b9df-7fe99a58764d with 9 rules
2025-07-11T05:24:49.383Z [DEBUG] - 📊 Started performance monitoring for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.384Z [DEBUG] - 📊 Started performance monitoring for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.387Z [DEBUG] - 📊 Started performance monitoring for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.387Z [DEBUG] - 🔗 Integrated monitoring started for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.391Z [DEBUG] - 📊 Registered active scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d (total: 1)
2025-07-11T05:24:49.415Z [INFO] - 🎛️ Starting Performance Automation System...
2025-07-11T05:24:49.437Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:24:49.448Z [INFO] - 📊 Real-Time Monitoring Dashboard started
2025-07-11T05:24:49.450Z [INFO] - ✅ Dashboard monitoring started
2025-07-11T05:24:49.455Z [INFO] - 🔌 Dashboard WebSocket Service started on port 8081
2025-07-11T05:24:49.455Z [INFO] - ✅ WebSocket service started
2025-07-11T05:24:49.458Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:24:49.459Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:24:49.461Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:24:49.462Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:24:49.463Z [INFO] - 🔮 Predictive Performance Analytics started
2025-07-11T05:24:49.464Z [INFO] - ✅ Predictive analytics started
2025-07-11T05:24:49.465Z [INFO] - 🎛️ Performance Automation System fully operational
2025-07-11T05:24:49.465Z [INFO] - 🎛️ Started Performance Automation Controller for scan monitoring
2025-07-11T05:24:49.467Z [DEBUG] - 🔍 Getting page for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:49.468Z [DEBUG] - 🌐 Creating new browser instance
2025-07-11T05:24:53.938Z [INFO] - ✅ Created new browser: browser-1752211493938-mubccd3ef
2025-07-11T05:24:54.673Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:24:58.611Z [DEBUG] - 🆕 Created new page for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:24:58.613Z [DEBUG] - ✅ URL validation passed for: https://tigerconnect.com/
2025-07-11T05:24:58.618Z [DEBUG] - 🔍 Checking connectivity for: https://tigerconnect.com/
2025-07-11T05:24:59.687Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:01.076Z [DEBUG] - ✅ Connectivity check passed for: https://tigerconnect.com/ (status: 200)
2025-07-11T05:25:01.170Z [INFO] - 🔗 Navigating to: https://tigerconnect.com/ (timeout: 60000ms)
2025-07-11T05:25:04.712Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:09.713Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:14.752Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:19.771Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:24.777Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:29.783Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:33.944Z [INFO] - ✅ Navigation completed
2025-07-11T05:25:33.974Z [INFO] - 🚀 Starting Phase 2 analysis: Website type detection and optimization
2025-07-11T05:25:33.981Z [DEBUG] - 🔍 Starting CMS platform detection
2025-07-11T05:25:34.072Z [DEBUG] - 🛒 Starting e-commerce accessibility analysis
2025-07-11T05:25:34.246Z [DEBUG] - 🎬 Starting media accessibility analysis
2025-07-11T05:25:34.270Z [DEBUG] - ⚛️ Starting framework-specific accessibility analysis
2025-07-11T05:25:34.862Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:36.677Z [WARN] - ⚠️ Phase 2 analysis encountered errors, continuing with standard analysis - {"error":"CMS detection failed - no results returned"}
2025-07-11T05:25:36.885Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:25:37.507Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:25:37.508Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 0% (0/9)
2025-07-11T05:25:37.522Z [INFO] - 🔍 Executing 9 WCAG checks...
2025-07-11T05:25:37.535Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:25:37.549Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:25:37.549Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 0% (0/9)
2025-07-11T05:25:37.552Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Minimum) (WCAG-010)
2025-07-11T05:25:37.570Z [INFO] - 🎯 Advanced Focus Tracker initialized - {"customIndicators":true,"flowAnalysis":true,"accessibilityTree":true}
2025-07-11T05:25:37.575Z [INFO] - 🌈 Wide Gamut Color Analyzer initialized - {"p3Analysis":true,"rec2020Analysis":true,"dynamicMonitoring":true}
2025-07-11T05:25:37.583Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-010: Focus Not Obscured (Minimum)
2025-07-11T05:25:38.052Z [DEBUG] - 🔍 Cache miss: rule:WCAG-010:2b050acac20b6b1d43405d6ab7a4fae3:659731ebf75384e3bc2f1e9e285145a8
2025-07-11T05:25:38.053Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-010, executing check
2025-07-11T05:25:38.057Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting WCAG-010: Focus Not Obscured (Minimum)
2025-07-11T05:25:39.882Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:44.908Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:49.533Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:25:49.534Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:25:49.538Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:25:49.540Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:25:49.880Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:25:54.892Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:00.424Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:06.017Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:11.017Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:13.647Z [WARN] - ⚠️ [65d64a12-a4dc-4241-b9df-7fe99a58764d] WCAG-010 failed: 34/100 (34.0%) - converted to 0% for WCAG compliance
2025-07-11T05:26:13.655Z [INFO] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Completed WCAG-010 in 35581ms - Status: failed (0/100)
2025-07-11T05:26:13.690Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-010
2025-07-11T05:26:13.789Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:26:13.794Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:26:13.803Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:26:13.810Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:26:17.757Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:26:17.758Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:18.760Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:26:18.782Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:26:18.865Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":5076}
2025-07-11T05:26:19.447Z [DEBUG] - 💾 Cached: rule:WCAG-010:2b050acac20b6b1d43405d6ab7a4fae3:659731ebf75384e3bc2f1e9e285145a8 (78057 bytes)
2025-07-11T05:26:19.448Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-010
2025-07-11T05:26:19.498Z [DEBUG] - 🔍 Cache miss: rule:WCAG-010:WCAG-010:dGV4dDpGb2N1cyBvYnN0cnVjdGlvbiBh
2025-07-11T05:26:19.762Z [DEBUG] - 📊 Reached max evidence items limit: 30
2025-07-11T05:26:19.763Z [DEBUG] - 💾 Cached: rule:WCAG-010:WCAG-010:dGV4dDpGb2N1cyBvYnN0cnVjdGlvbiBh (34734 bytes)
2025-07-11T05:26:19.764Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T05:26:19.764Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:26:19.765Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:26:19.766Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:26:19.824Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:26:20.856Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:26:20.881Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:26:20.882Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1119}
2025-07-11T05:26:20.906Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-010: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:26:20.957Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-010","Utility errors detected for WCAG-010: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:26:20.983Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-010
2025-07-11T05:26:20.986Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-010: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:26:20.988Z [DEBUG] - 🔧 Utility performance recorded for WCAG-010: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:26:20.989Z [DEBUG] - 🔧 Utility analysis completed for WCAG-010: - {"utilitiesUsed":2,"errors":1,"executionTime":43331}
2025-07-11T05:26:21.047Z [DEBUG] - ⏱️ Check WCAG-010 completed in 43471ms (success: true)
2025-07-11T05:26:21.070Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:26:21.354Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:26:21.354Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 11% (1/9)
2025-07-11T05:26:21.357Z [INFO] - ✅ Rule WCAG-010 completed: failed (0/100)
2025-07-11T05:26:21.371Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:26:21.607Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:26:21.608Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 11% (1/9)
2025-07-11T05:26:21.616Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Enhanced) (WCAG-011)
2025-07-11T05:26:21.646Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-011: Focus Not Obscured (Enhanced)
2025-07-11T05:26:22.112Z [DEBUG] - 🔍 Cache miss: rule:WCAG-011:2b050acac20b6b1d43405d6ab7a4fae3:1b3f6049c2fd04a00f8191576e3a24c5
2025-07-11T05:26:22.113Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-011, executing check
2025-07-11T05:26:22.115Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting WCAG-011: Focus Not Obscured (Enhanced)
2025-07-11T05:26:22.760Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:28.447Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:33.566Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:38.778Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:39.205Z [WARN] - ⚠️ Insufficient data for trend analysis
2025-07-11T05:26:39.264Z [WARN] - ⚠️ Insufficient data for correlation analysis
2025-07-11T05:26:40.577Z [WARN] - Memory leak detected - {"type":"heap","sizeMB":475,"growthRate":6.222222222222222,"severity":"low"}
2025-07-11T05:26:44.143Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:49.425Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:26:49.485Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:26:49.486Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:26:49.486Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:26:49.486Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:26:54.461Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:00.250Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:05.288Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:10.325Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:10.734Z [WARN] - Memory leak detected - {"type":"heap","sizeMB":477,"growthRate":6.666666666666667,"severity":"low"}
2025-07-11T05:27:15.398Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:21.352Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:26.597Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:31.704Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:37.136Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:41.172Z [WARN] - Memory leak detected - {"type":"heap","sizeMB":481,"growthRate":7.333333333333333,"severity":"low"}
2025-07-11T05:27:42.207Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:47.206Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:48.536Z [WARN] - ⚠️ [65d64a12-a4dc-4241-b9df-7fe99a58764d] WCAG-011 failed: 94/100 (94.0%) - converted to 0% for WCAG compliance
2025-07-11T05:27:48.537Z [INFO] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Completed WCAG-011 in 86421ms - Status: failed (0/100)
2025-07-11T05:27:48.539Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-011
2025-07-11T05:27:48.541Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:27:48.551Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:48.553Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:27:48.554Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:27:48.691Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:27:49.488Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:27:49.489Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:27:49.492Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:27:49.496Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:27:49.699Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:49.756Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:27:49.757Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1215}
2025-07-11T05:27:49.801Z [DEBUG] - 💾 Cached: rule:WCAG-011:2b050acac20b6b1d43405d6ab7a4fae3:1b3f6049c2fd04a00f8191576e3a24c5 (72761 bytes)
2025-07-11T05:27:49.801Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-011
2025-07-11T05:27:49.804Z [DEBUG] - 🔍 Cache miss: rule:WCAG-011:WCAG-011:dGV4dDpFbmhhbmNlZCBmb2N1cyB2aXNp
2025-07-11T05:27:49.806Z [DEBUG] - 🔍 Skipping low-quality evidence: Enhanced focus visibility analysis summary
2025-07-11T05:27:49.809Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T05:27:49.810Z [DEBUG] - 💾 Cached: rule:WCAG-011:WCAG-011:dGV4dDpFbmhhbmNlZCBmb2N1cyB2aXNp (31808 bytes)
2025-07-11T05:27:49.811Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T05:27:49.812Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:49.815Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:27:49.816Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:27:49.876Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:27:50.876Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:50.944Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:27:50.944Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1133}
2025-07-11T05:27:50.950Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-011: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:27:50.950Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-011","Utility errors detected for WCAG-011: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:27:50.952Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-011
2025-07-11T05:27:50.953Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-011: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:27:50.956Z [DEBUG] - 🔧 Utility performance recorded for WCAG-011: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:27:50.956Z [DEBUG] - 🔧 Utility analysis completed for WCAG-011: - {"utilitiesUsed":2,"errors":1,"executionTime":89333}
2025-07-11T05:27:50.957Z [DEBUG] - ⏱️ Check WCAG-011 completed in 89341ms (success: true)
2025-07-11T05:27:50.958Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:51.230Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:51.230Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 22% (2/9)
2025-07-11T05:27:51.232Z [INFO] - ✅ Rule WCAG-011 completed: failed (0/100)
2025-07-11T05:27:51.235Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:51.348Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:51.349Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 22% (2/9)
2025-07-11T05:27:51.352Z [INFO] - 🔍 Executing rule: Focus Appearance (WCAG-012)
2025-07-11T05:27:51.362Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-012: Focus Appearance
2025-07-11T05:27:51.478Z [DEBUG] - 🔍 Cache miss: rule:WCAG-012:2b050acac20b6b1d43405d6ab7a4fae3:049c959d2823ba0d57461e7b8cb5c4be
2025-07-11T05:27:51.480Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-012, executing check
2025-07-11T05:27:51.483Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting WCAG-012: Focus Appearance
2025-07-11T05:27:51.566Z [DEBUG] - 🌈 Starting wide gamut color analysis
2025-07-11T05:27:51.606Z [DEBUG] - 🎨 Starting enhanced color contrast analysis
Advanced focus appearance analysis failed, falling back to basic analysis: Error [TypeError]: element.className.split is not a function
    at evaluate (evaluate at EnhancedColorAnalyzer.analyzePageContrast (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\enhanced-color-analyzer.ts:66:41), <anonymous>:7:33)
    at ExecutionContext.#evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\cdp\ExecutionContext.ts:452:34)
    at async ExecutionContext.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\cdp\ExecutionContext.ts:291:12)
    at async IsolatedWorld.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\cdp\IsolatedWorld.ts:196:12)
    at async CdpFrame.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\api\Frame.ts:490:12)
    at async CdpPage.evaluate (D:\Web projects\Comply Checker\node_modules\puppeteer-core\src\api\Page.ts:2308:12)
    at async EnhancedColorAnalyzer.analyzePageContrast (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\enhanced-color-analyzer.ts:149:26)
    at async WideGamutColorAnalyzer.analyzeWideGamutColors (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\wide-gamut-color-analyzer.ts:180:26)
    at async FocusAppearanceCheck.executeFocusAppearanceCheck (D:\Web projects\Comply Checker\backend\src\compliance\wcag\checks\focus-appearance.ts:113:34)
    at async EnhancedCheckTemplate.executeCheck (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\check-template.ts:73:24)
2025-07-11T05:27:52.362Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:54.782Z [WARN] - ⚠️ [65d64a12-a4dc-4241-b9df-7fe99a58764d] WCAG-012 failed: 22/100 (22.0%) - converted to 0% for WCAG compliance
2025-07-11T05:27:54.783Z [INFO] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Completed WCAG-012 in 3299ms - Status: failed (0/100)
2025-07-11T05:27:54.786Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-012
2025-07-11T05:27:54.787Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:27:54.790Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:54.793Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:27:54.796Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:27:54.904Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:27:55.942Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:55.984Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:27:55.985Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1198}
2025-07-11T05:27:55.988Z [DEBUG] - 💾 Cached: rule:WCAG-012:2b050acac20b6b1d43405d6ab7a4fae3:049c959d2823ba0d57461e7b8cb5c4be (59398 bytes)
2025-07-11T05:27:55.989Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-012
2025-07-11T05:27:55.991Z [DEBUG] - 🔍 Cache miss: rule:WCAG-012:WCAG-012:dGV4dDpGb2N1cyBhcHBlYXJhbmNlIGFu
2025-07-11T05:27:55.992Z [DEBUG] - 📊 Reached max evidence items limit: 25
2025-07-11T05:27:55.993Z [DEBUG] - 💾 Cached: rule:WCAG-012:WCAG-012:dGV4dDpGb2N1cyBhcHBlYXJhbmNlIGFu (19513 bytes)
2025-07-11T05:27:55.996Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T05:27:55.999Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:56.002Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:27:56.004Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:27:56.070Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:27:57.095Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:57.369Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:27:57.544Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:27:57.553Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1557}
2025-07-11T05:27:57.561Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-012: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:27:57.564Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-012","Utility errors detected for WCAG-012: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:27:57.567Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-012
2025-07-11T05:27:57.568Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-012: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:27:57.569Z [DEBUG] - 🔧 Utility performance recorded for WCAG-012: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:27:57.571Z [DEBUG] - 🔧 Utility analysis completed for WCAG-012: - {"utilitiesUsed":2,"errors":1,"executionTime":6209}
2025-07-11T05:27:57.573Z [DEBUG] - ⏱️ Check WCAG-012 completed in 6221ms (success: true)
2025-07-11T05:27:57.575Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:57.983Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:57.983Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 33% (3/9)
2025-07-11T05:27:57.985Z [INFO] - ✅ Rule WCAG-012 completed: failed (0/100)
2025-07-11T05:27:57.988Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:58.215Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:27:58.215Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 33% (3/9)
2025-07-11T05:27:58.217Z [INFO] - 🔍 Executing rule: Dragging Movements (WCAG-013)
2025-07-11T05:27:58.221Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-013: Dragging Movements
2025-07-11T05:27:58.321Z [DEBUG] - 🔍 Cache miss: rule:WCAG-013:2b050acac20b6b1d43405d6ab7a4fae3:5666d327c6c50696d33438b7c9208552
2025-07-11T05:27:58.323Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-013, executing check
2025-07-11T05:27:58.325Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Executing manual review check WCAG-013
2025-07-11T05:27:59.211Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-013
2025-07-11T05:27:59.212Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:27:59.214Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:27:59.219Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:27:59.291Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:00.295Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:00.333Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:00.361Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1148}
2025-07-11T05:28:00.368Z [DEBUG] - 💾 Cached: rule:WCAG-013:2b050acac20b6b1d43405d6ab7a4fae3:5666d327c6c50696d33438b7c9208552 (3004 bytes)
2025-07-11T05:28:00.375Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-013
2025-07-11T05:28:00.379Z [DEBUG] - 🔍 Cache miss: rule:WCAG-013:WCAG-013:dGV4dDpObyBkcmFnIGFuZCBkcm9wIGVs
2025-07-11T05:28:00.393Z [DEBUG] - 💾 Cached: rule:WCAG-013:WCAG-013:dGV4dDpObyBkcmFnIGFuZCBkcm9wIGVs (4316 bytes)
2025-07-11T05:28:00.394Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T05:28:00.399Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:00.402Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:00.560Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:01.573Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:01.995Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:01.996Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1602}
2025-07-11T05:28:02.000Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-013: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:28:02.003Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-013","Utility errors detected for WCAG-013: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:28:02.005Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-013
2025-07-11T05:28:02.008Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-013: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:28:02.010Z [DEBUG] - 🔧 Utility performance recorded for WCAG-013: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:28:02.012Z [DEBUG] - 🔧 Utility analysis completed for WCAG-013: - {"utilitiesUsed":1,"errors":1,"executionTime":3783}
2025-07-11T05:28:02.018Z [DEBUG] - ⏱️ Check WCAG-013 completed in 3801ms (success: true)
2025-07-11T05:28:02.024Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:02.236Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:02.237Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 44% (4/9)
2025-07-11T05:28:02.239Z [INFO] - ✅ Rule WCAG-013 completed: failed (0/100)
2025-07-11T05:28:02.240Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:02.355Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:02.356Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 44% (4/9)
2025-07-11T05:28:02.358Z [INFO] - 🔍 Executing rule: Target Size (Minimum) (WCAG-014)
2025-07-11T05:28:02.389Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-014: Target Size
2025-07-11T05:28:02.390Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:02.644Z [DEBUG] - 🔍 Cache miss: rule:WCAG-014:2b050acac20b6b1d43405d6ab7a4fae3:844ad96101674e4953acdc3441917f7d
2025-07-11T05:28:02.644Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-014, executing check
2025-07-11T05:28:02.647Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting WCAG-014: Target Size
2025-07-11T05:28:02.651Z [DEBUG] - 🎯 Starting enhanced target size analysis
2025-07-11T05:28:03.254Z [WARN] - ⚠️ [65d64a12-a4dc-4241-b9df-7fe99a58764d] WCAG-014 failed: 94/100 (94.0%) - converted to 0% for WCAG compliance
2025-07-11T05:28:03.255Z [INFO] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Completed WCAG-014 in 607ms - Status: failed (0/100)
2025-07-11T05:28:03.258Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-014
2025-07-11T05:28:03.260Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:28:03.261Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:03.264Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:03.337Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:04.349Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:04.395Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:04.395Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1135}
2025-07-11T05:28:04.407Z [DEBUG] - 💾 Cached: rule:WCAG-014:2b050acac20b6b1d43405d6ab7a4fae3:844ad96101674e4953acdc3441917f7d (103687 bytes)
2025-07-11T05:28:04.408Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-014
2025-07-11T05:28:04.409Z [DEBUG] - 🔍 Cache miss: rule:WCAG-014:WCAG-014:dGV4dDpUYXJnZXQgc2l6ZSBhbmFseXNp
2025-07-11T05:28:04.410Z [DEBUG] - 🔍 Skipping low-quality evidence: Target size analysis summary
2025-07-11T05:28:04.413Z [DEBUG] - 📊 Reached max evidence items limit: 40
2025-07-11T05:28:04.416Z [DEBUG] - 💾 Cached: rule:WCAG-014:WCAG-014:dGV4dDpUYXJnZXQgc2l6ZSBhbmFseXNp (68607 bytes)
2025-07-11T05:28:04.418Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-11T05:28:04.420Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:04.422Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:04.481Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:05.483Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:05.706Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:05.707Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1289}
2025-07-11T05:28:05.709Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-014: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:28:05.711Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-014","Utility errors detected for WCAG-014: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:28:05.769Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-014
2025-07-11T05:28:05.789Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-014: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:28:05.858Z [DEBUG] - 🔧 Utility performance recorded for WCAG-014: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:28:05.880Z [DEBUG] - 🔧 Utility analysis completed for WCAG-014: - {"utilitiesUsed":1,"errors":1,"executionTime":3351}
2025-07-11T05:28:05.890Z [DEBUG] - ⏱️ Check WCAG-014 completed in 3532ms (success: true)
2025-07-11T05:28:05.924Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:06.106Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:06.107Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 56% (5/9)
2025-07-11T05:28:06.113Z [INFO] - ✅ Rule WCAG-014 completed: failed (0/100)
2025-07-11T05:28:06.114Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:06.186Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:06.187Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 56% (5/9)
2025-07-11T05:28:06.189Z [INFO] - 🔍 Executing rule: Consistent Help (WCAG-015)
2025-07-11T05:28:06.222Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-015: Consistent Help
2025-07-11T05:28:06.381Z [DEBUG] - 🔍 Cache miss: rule:WCAG-015:2b050acac20b6b1d43405d6ab7a4fae3:c6e979e636d3a2648d516983248f4dc1
2025-07-11T05:28:06.383Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-015, executing check
2025-07-11T05:28:06.386Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting WCAG-015: Consistent Help
2025-07-11T05:28:06.552Z [INFO] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Completed WCAG-015 in 166ms - Status: passed (100/100)
2025-07-11T05:28:06.553Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-015
2025-07-11T05:28:06.555Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:28:06.557Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:06.633Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T05:28:07.019Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T05:28:07.065Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:07.399Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:08.073Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:08.092Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:08.093Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality"],"confidence":0.6,"accuracy":0.30000000000000004,"executionTime":1538}
2025-07-11T05:28:08.095Z [DEBUG] - 💾 Cached: rule:WCAG-015:2b050acac20b6b1d43405d6ab7a4fae3:c6e979e636d3a2648d516983248f4dc1 (2209 bytes)
2025-07-11T05:28:08.095Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-015
2025-07-11T05:28:08.096Z [DEBUG] - 🔍 Cache miss: rule:WCAG-035:WCAG-035:aW5mbzpGb3VuZCA1IGhlbHAgbWVjaGFu
2025-07-11T05:28:08.098Z [DEBUG] - 💾 Cached: rule:WCAG-035:WCAG-035:aW5mbzpGb3VuZCA1IGhlbHAgbWVjaGFu (3122 bytes)
2025-07-11T05:28:08.099Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-11T05:28:08.100Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:08.101Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-11T05:28:08.169Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-11T05:28:08.188Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:09.195Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:09.232Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:09.233Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality"],"confidence":0.6,"accuracy":0.30000000000000004,"executionTime":1134}
2025-07-11T05:28:09.236Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-015: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:28:09.238Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-015","Utility errors detected for WCAG-015: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:28:09.239Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-015
2025-07-11T05:28:09.240Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-015: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:28:09.241Z [DEBUG] - 🔧 Utility performance recorded for WCAG-015: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:28:09.243Z [DEBUG] - 🔧 Utility analysis completed for WCAG-015: - {"utilitiesUsed":1,"errors":1,"executionTime":3047}
2025-07-11T05:28:09.244Z [DEBUG] - ⏱️ Check WCAG-015 completed in 3055ms (success: true)
2025-07-11T05:28:09.245Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:09.754Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:09.754Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 67% (6/9)
2025-07-11T05:28:09.758Z [INFO] - ✅ Rule WCAG-015 completed: passed (100/100)
2025-07-11T05:28:09.763Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:09.891Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:09.897Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 67% (6/9)
2025-07-11T05:28:09.899Z [INFO] - 🔍 Executing rule: Redundant Entry (WCAG-016)
2025-07-11T05:28:09.924Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-016: Redundant Entry
2025-07-11T05:28:10.141Z [DEBUG] - 🔍 Cache miss: rule:WCAG-016:2b050acac20b6b1d43405d6ab7a4fae3:b4fa02fc20908e9ce39ed45e000f147e
2025-07-11T05:28:10.142Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-016, executing check
2025-07-11T05:28:10.144Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Executing manual review check WCAG-016
2025-07-11T05:28:10.221Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-016
2025-07-11T05:28:10.258Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:28:10.260Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:10.263Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:10.315Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:11.178Z [WARN] - Memory leak detected - {"type":"heap","sizeMB":490,"growthRate":5.777777777777778,"severity":"low"}
2025-07-11T05:28:11.361Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:11.379Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:11.380Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1122}
2025-07-11T05:28:11.381Z [DEBUG] - 💾 Cached: rule:WCAG-016:2b050acac20b6b1d43405d6ab7a4fae3:b4fa02fc20908e9ce39ed45e000f147e (1275 bytes)
2025-07-11T05:28:11.382Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-016
2025-07-11T05:28:11.384Z [DEBUG] - 🔍 Cache miss: rule:WCAG-016:WCAG-016:dGV4dDpSZWR1bmRhbnQgZW50cnkgYW5h
2025-07-11T05:28:11.387Z [DEBUG] - 💾 Cached: rule:WCAG-016:WCAG-016:dGV4dDpSZWR1bmRhbnQgZW50cnkgYW5h (1576 bytes)
2025-07-11T05:28:11.388Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-11T05:28:11.389Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:11.390Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:11.606Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:12.415Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:12.616Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:12.637Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:12.638Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library"],"confidence":0.6,"accuracy":0.4,"executionTime":1250}
2025-07-11T05:28:12.639Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-016: - {"utilitiesUsed":1,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:28:12.640Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-016","Utility errors detected for WCAG-016: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:28:12.641Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-016
2025-07-11T05:28:12.642Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-016: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:28:12.644Z [DEBUG] - 🔧 Utility performance recorded for WCAG-016: - {"utilitiesUsed":1,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:28:12.645Z [DEBUG] - 🔧 Utility analysis completed for WCAG-016: - {"utilitiesUsed":1,"errors":1,"executionTime":2740}
2025-07-11T05:28:12.646Z [DEBUG] - ⏱️ Check WCAG-016 completed in 2747ms (success: true)
2025-07-11T05:28:12.647Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:12.868Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:12.868Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 78% (7/9)
2025-07-11T05:28:12.871Z [INFO] - ✅ Rule WCAG-016 completed: failed (0/100)
2025-07-11T05:28:12.875Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:12.984Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:12.985Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 78% (7/9)
2025-07-11T05:28:12.987Z [INFO] - 🔍 Executing rule: Accessible Authentication (Minimum) (WCAG-022)
2025-07-11T05:28:12.997Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting enhanced WCAG-022: Accessible Authentication (Minimum)
2025-07-11T05:28:13.093Z [DEBUG] - 🔍 Cache miss: rule:WCAG-022:2b050acac20b6b1d43405d6ab7a4fae3:a916f1b5c41fd4a4001d720d0fd2b0ea
2025-07-11T05:28:13.093Z [DEBUG] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cache miss for WCAG-022, executing check
2025-07-11T05:28:13.095Z [INFO] - 🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Executing manual review check WCAG-022
2025-07-11T05:28:13.140Z [ERROR] - Error in accessible authentication check: - {"error":"this.checkForAlternatives is not a function\npptr:evaluate;AccessibleAuthenticationCheck.detectAuthenticationElements%20(D%3A%5CWeb%20projects%5CComply%20Checker%5Cbackend%5Csrc%5Ccompliance%5Cwcag%5Cchecks%5Caccessible-authentication.ts%3A234%3A27):54:49\nNodeList.forEach (<anonymous>)"}
2025-07-11T05:28:13.141Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Applying utility enhancements for WCAG-022
2025-07-11T05:28:13.145Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-11T05:28:13.146Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:13.148Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:28:13.149Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:13.512Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:14.524Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:14.560Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:14.561Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1416}
2025-07-11T05:28:14.565Z [DEBUG] - 💾 Cached: rule:WCAG-022:2b050acac20b6b1d43405d6ab7a4fae3:a916f1b5c41fd4a4001d720d0fd2b0ea (1431 bytes)
2025-07-11T05:28:14.566Z [DEBUG] - 💾 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Cached result for WCAG-022
2025-07-11T05:28:14.569Z [DEBUG] - 🔍 Cache miss: rule:WCAG-022:WCAG-022:ZXJyb3I6RXJyb3IgZHVyaW5nIGF1dGhl
2025-07-11T05:28:14.571Z [DEBUG] - 💾 Cached: rule:WCAG-022:WCAG-022:ZXJyb3I6RXJyb3IgZHVyaW5nIGF1dGhl (1372 bytes)
2025-07-11T05:28:14.576Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T05:28:14.577Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:14.610Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:28:14.633Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:14.743Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:15.750Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:15.769Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:15.770Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1194}
2025-07-11T05:28:15.771Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-022: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:28:15.796Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-022","Utility errors detected for WCAG-022: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:28:15.800Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-022
2025-07-11T05:28:15.801Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-022: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:28:15.803Z [DEBUG] - 🔧 Utility performance recorded for WCAG-022: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:28:15.804Z [DEBUG] - 🔧 Utility analysis completed for WCAG-022: - {"utilitiesUsed":2,"errors":1,"executionTime":2784}
2025-07-11T05:28:15.806Z [DEBUG] - ⏱️ Check WCAG-022 completed in 2819ms (success: true)
2025-07-11T05:28:15.807Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:16.114Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:16.114Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 89% (8/9)
2025-07-11T05:28:16.116Z [INFO] - ✅ Rule WCAG-022 completed: failed (0/100)
2025-07-11T05:28:16.119Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:16.241Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:16.241Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 89% (8/9)
2025-07-11T05:28:16.244Z [INFO] - 🔍 Executing rule: Accessible Authentication (Enhanced) (WCAG-023)
🔍 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting WCAG-037: Accessible Authentication (Enhanced) (40% automated)
2025-07-11T05:28:16.319Z [ERROR] - Error in enhanced authentication check: - {"error":"this.findCognitiveAlternatives is not a function"}
⚠️ [65d64a12-a4dc-4241-b9df-7fe99a58764d] WCAG-037 failed: 0/100 (0.0%) - converted to 0% for WCAG compliance
✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Completed WCAG-037 in 36ms - Status: failed (0/100, Manual items: 1)
2025-07-11T05:28:16.324Z [DEBUG] - 🔍 Cache miss: rule:WCAG-037:WCAG-037:ZXJyb3I6RXJyb3IgZHVyaW5nIGVuaGFu
2025-07-11T05:28:16.325Z [DEBUG] - 💾 Cached: rule:WCAG-037:WCAG-037:ZXJyb3I6RXJyb3IgZHVyaW5nIGVuaGFu (2780 bytes)
2025-07-11T05:28:16.326Z [DEBUG] - 🔧 [65d64a12-a4dc-4241-b9df-7fe99a58764d] Starting utility analysis for WCAG-023 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-11T05:28:16.327Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:16.329Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-11T05:28:16.330Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-11T05:28:16.377Z [WARN] - ⚠️ Utility pattern-validation failed on attempt 1, retrying... - {"error":"elements.map is not a function","attempt":1,"maxRetries":2}
2025-07-11T05:28:17.378Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-11T05:28:17.399Z [WARN] - ❌ Utility pattern-validation failed after 2 attempts, using fallback - {"error":"elements.map is not a function"}
2025-07-11T05:28:17.400Z [DEBUG] - ✅ [65d64a12-a4dc-4241-b9df-7fe99a58764d] Utility analysis completed for WCAG-023 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.65,"executionTime":1074}
2025-07-11T05:28:17.401Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-023: - {"utilitiesUsed":2,"cacheHitRate":0,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-11T05:28:17.402Z [WARN] - ⚠️ Performance alerts for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"alerts":["Low cache hit rate (0.0%) for WCAG-023","Utility errors detected for WCAG-023: pattern-validation: elements.map is not a function (after 2 attempts)"]}
2025-07-11T05:28:17.403Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Low cache hit rate (0.0%) for WCAG-023
2025-07-11T05:28:17.404Z [INFO] - Alert for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: Utility errors detected for WCAG-023: pattern-validation: elements.map is not a function (after 2 attempts)
2025-07-11T05:28:17.406Z [DEBUG] - 🔧 Utility performance recorded for WCAG-023: - {"utilitiesUsed":2,"cacheHitRate":"0.0%","utilityOverhead":"0.0%"}
2025-07-11T05:28:17.407Z [DEBUG] - 🔧 Utility analysis completed for WCAG-023: - {"utilitiesUsed":2,"errors":1,"executionTime":1157}
2025-07-11T05:28:17.408Z [DEBUG] - ⏱️ Check WCAG-023 completed in 1164ms (success: true)
2025-07-11T05:28:17.409Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:17.426Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:17.699Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> running
2025-07-11T05:28:17.699Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 100% (9/9)
2025-07-11T05:28:17.704Z [INFO] - ✅ Rule WCAG-023 completed: failed (0/100)
2025-07-11T05:28:17.706Z [INFO] - ✅ All checks completed: 9 results
🔍 WCAG Scoring Debug - Input Results: [
  {
    ruleId: 'WCAG-010',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-011',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-012',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-013',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-014',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-015',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-016',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-022',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.05
  },
  {
    ruleId: 'WCAG-023',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.03
  }
]
📊 Rule WCAG-010: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-011: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-012: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-013: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-014: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-015: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-016: score=0/100 (0.0%), weight=0.003
📊 Rule WCAG-022: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-023: score=0/100 (0.0%), weight=0.003
🎯 WCAG Final Score Calculation:
      - Total Weighted Score: 0.27
      - Total Weight: 0.036
      - Final Score: 7%
      - Rules Processed: 9/9
      - Passed Rules: 1
      - Failed Rules: 8
2025-07-11T05:28:17.827Z [INFO] - 💾 Saving WCAG scan result: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:17.827Z [INFO] - 📊 Scan summary: {"scanId":"65d64a12-a4dc-4241-b9df-7fe99a58764d","totalRules":9,"overallScore":7,"levelAchieved":"None","riskLevel":"Critical"}
2025-07-11T05:28:17.852Z [INFO] - 💾 Saving WCAG scan result: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:18.657Z [INFO] - ✅ WCAG scan result saved: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:18.657Z [INFO] - ✅ WCAG scan result saved successfully: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:18.660Z [INFO] - 📝 Storing 1 manual review items for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:18.936Z [INFO] - ✅ Stored 1 manual review items for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:18.936Z [INFO] - ✅ Stored 1 manual review items for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:18.940Z [INFO] - 📝 Updating WCAG scan status: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> completed
2025-07-11T05:28:19.055Z [INFO] - ✅ WCAG scan status updated: 65d64a12-a4dc-4241-b9df-7fe99a58764d -> completed
2025-07-11T05:28:19.055Z [INFO] - 📈 Scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: 100% (9/9)
2025-07-11T05:28:19.057Z [INFO] - ✅ Comprehensive WCAG scan completed: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:19.059Z [INFO] - 📊 Overall Score: 7% | Level: None | Risk: Critical
2025-07-11T05:28:19.069Z [INFO] - 📈 New performance baseline established - {"performanceScore":80,"averageCheckDuration":17350.11111111111,"memoryPeakMB":513}
2025-07-11T05:28:19.070Z [INFO] - 📊 Performance report generated for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d - {"duration":209683,"checksExecuted":9,"successRate":100,"memoryPeak":513,"performanceScore":80}
2025-07-11T05:28:19.071Z [INFO] - 📊 Performance report for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d: - {"duration":209683,"performanceScore":80,"memoryPeak":513,"recommendations":["Consider enabling more aggressive caching to reduce scan time","Optimize slow checks: WCAG-011","Low browser pool efficiency - consider increasing pool size"]}
2025-07-11T05:28:22.429Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:22.542Z [DEBUG] - ✅ Released page back to pool for scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:22.543Z [DEBUG] - 📊 Unregistered active scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d (total: 0)
2025-07-11T05:28:22.544Z [INFO] - ✅ [03e0ec09-d2b5-48b6-909e-ff33a1ecb0a0] WCAG scan completed successfully: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:28:27.432Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:32.437Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:37.457Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:42.476Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:47.480Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:47.909Z [INFO] - 🔓 [016f581b-9e06-4bd4-903d-a39f977c652e] Development mode: Mock authentication applied
2025-07-11T05:28:47.930Z [INFO] - 📋 [016f581b-9e06-4bd4-903d-a39f977c652e] Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b
2025-07-11T05:28:47.934Z [INFO] - 📊 Fetching WCAG scans for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b - {"options":{"page":1,"limit":20,"sortBy":"scanTimestamp","sortOrder":"desc"}}
2025-07-11T05:28:49.494Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:28:49.499Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:28:49.504Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:28:49.506Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:28:52.494Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:28:57.499Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:02.509Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:07.531Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:11.580Z [INFO] - 🔓 [e753020b-bee4-41b7-98e8-0f0ffa7fc168] Development mode: Mock authentication applied
2025-07-11T05:29:11.595Z [INFO] - 🔍 [e753020b-bee4-41b7-98e8-0f0ffa7fc168] Fetching WCAG scan details: 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:29:11.611Z [INFO] - 🔍 Fetching WCAG scan: 65d64a12-a4dc-4241-b9df-7fe99a58764d for user: 9fed30a7-64b4-4ffe-b531-6e9cf592952b
2025-07-11T05:29:11.712Z [INFO] - 📋 Fetching manual review items for scan 65d64a12-a4dc-4241-b9df-7fe99a58764d
2025-07-11T05:29:11.725Z [INFO] - 🔍 Debug - Manual review items fetched: 1 - {"manualReviewItems":[{"id":"1b648d64-c30f-45dc-9a4d-c1b7070399ac","ruleId":"WCAG-023","selector":"body","description":"Manual enhanced authentication review required","automatedFindings":"Automated analysis failed","reviewRequired":"Manually review all authentication mechanisms for enhanced accessibility compliance","priority":"high","estimatedTime":60,"reviewStatus":"pending","reviewerNotes":null,"reviewAssessment":null,"reviewedAt":null,"reviewedBy":null}]}
2025-07-11T05:29:12.534Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:17.540Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:22.548Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:27.556Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:55.398Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:29:55.407Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:29:55.408Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:29:55.409Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:29:55.410Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:29:56.008Z [WARN] - 🔌 Browser disconnected: browser-1752211493938-mubccd3ef
2025-07-11T05:29:58.382Z [DEBUG] - 🗑️ Cleaned up old browser: browser-1752211493938-mubccd3ef
2025-07-11T05:30:00.399Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:05.413Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:10.413Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:15.418Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:20.419Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:25.433Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:30.442Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:35.452Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:40.464Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:45.477Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:50.485Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:30:55.426Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:30:55.427Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:30:55.428Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:30:55.429Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:30:55.489Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:00.497Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:05.499Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:10.508Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:15.520Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:20.525Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:25.538Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:30.552Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:35.568Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:38.948Z [WARN] - ⚠️ Insufficient data for trend analysis
2025-07-11T05:31:38.948Z [WARN] - ⚠️ Insufficient data for correlation analysis
2025-07-11T05:31:40.569Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:45.588Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:50.590Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:31:55.441Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:31:55.442Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:31:55.443Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:31:55.445Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:31:55.610Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:00.627Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:05.662Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:10.704Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:15.714Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:20.716Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:25.723Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:30.724Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:35.744Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:40.762Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:45.783Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:50.787Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-11T05:32:55.456Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-11T05:32:55.457Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-11T05:32:55.462Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-11T05:32:55.463Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-11T05:32:55.806Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}